import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UserModule } from './controllers/user/user.module';
import { AuthModule } from './controllers/Auth/auth.module';
import { OrganizationModule } from './controllers/organization/organization.module';
import { AdminModule } from './controllers/admin/admin.module';
import { FormsRepositoryModule } from './controllers/formsrepository/formsrepository.module';
import { FormFieldsModule } from './controllers/formfields/formfields.module';
import {
  MongoDBConnection,
  MySQLConnection,
} from './util/modules/database.module';
import { AppsModule } from './controllers/apps/apps.module';
import { FormValuesModule } from './controllers/formvalues/formvalues.module';
import { FormSectionsRepositoryModule } from './controllers/formsections/form-sections.module';
import { ClientsRepositoryModule } from './controllers/clientsrepository/clientsrepository.module';
import { CountryStateCityModule } from './controllers/country-state-city/country-state-city.module';
import { ThemesModule } from './controllers/themes/themes.module';
import { ConfigurationsModule } from './controllers/configurations/configurations.module';
import { IndustryTypesModule } from './controllers/industry-types/industry-types.module';
import { IndustryAppProcessModule } from './controllers/industry-app-process/industry-app-process.module';
import { OrgAppConfigurationModule } from './controllers/org-app-configuration/org-app-configuration.module';
import { OnBoardOrgChecklistModule } from './controllers/onboard-org-checklist-repository/onboard-org-checklist-repository.module';
import { OnboardingEmployeeModule } from './controllers/employee/onboarding-employee.module';
import { OnBoardEmpChecklistModule } from './controllers/onboard-emp-checklist-repository/onboard-emp-checklist-repository.module';
import { PrimaryFormsRepositoryModule } from './controllers/primary-forms-repository/primary-forms-repository.module';
import { ScheduleModule } from '@nestjs/schedule';
import { OrgAppDashboardConfigurationModule } from './controllers/org-app-dashboard-configuration/org-app-dashboard-configuration.module';
import { FormResponseAsEmailModule } from './controllers/form-response-as-email/form-response-as-email.module';
import { CaregiversModule } from './controllers/caregivers/caregivers.module';
import StageModule from './controllers/stages/stage.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { InjectRequestInterceptor } from './util/interceptor/request.interceptor';

@Module({
  imports: [
    AppsModule,
    UserModule,
    ConfigurationsModule,
    AuthModule,
    AdminModule,
    MySQLConnection,
    FormFieldsModule,
    ThemesModule,
    MongoDBConnection,
    OrganizationModule,
    FormsRepositoryModule,
    CountryStateCityModule,
    ClientsRepositoryModule,
    FormSectionsRepositoryModule,
    FormValuesModule,
    IndustryTypesModule,
    IndustryAppProcessModule,
    OrgAppConfigurationModule,
    OnboardingEmployeeModule,
    OnBoardOrgChecklistModule,
    OnBoardEmpChecklistModule,
    PrimaryFormsRepositoryModule,
    OrgAppDashboardConfigurationModule,
    FormResponseAsEmailModule,
    CaregiversModule,
    ConfigModule.forRoot({ isGlobal: true }),
    ScheduleModule.forRoot(),
    StageModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: InjectRequestInterceptor,
    },
  ],
})
export class AppModule {}
