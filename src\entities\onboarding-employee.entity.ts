import {
  Column,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  JoinTable,
  ManyToMany,
  OneToOne,
  PrimaryColumn,
} from 'typeorm';

import { Apps } from './apps.entity';
import { Organization } from './organization.entity';

export enum EMPLOYEE_STATUS {
  IN_PROGRESS = 'In Progress',
  APPLICATION_COMPLETED = 'Application Completed',
  INTERVIEW_SCHEDULED = 'Interview Scheduled',
  INTERVIEW_RE_SCHEDULED = 'Interview Re Scheduled',
  INTERVIEW_COMPLETED = 'Interview Completed',
  IN_PERSON_ORIENTATION_COMPLETED = 'In Person Orientation Completed',
  EMPLOYEE_ONBOARD_COMPLETED = 'Employee Onboard Completed',
  REJECTED = 'Rejected',
}

@Entity()
export class OnboardingEmployee {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  onboarding_employee_id: string;

  @Column()
  name: string;

  @Column()
  email: string;

  @Column()
  mobile_number: string;

  @Column({ nullable: false })
  password: string;

  @ManyToOne(
    () => Organization,
    (organization: Organization) => organization.id,
    { eager: false, nullable: false },
  )
  @JoinColumn()
  organization: Organization;

  // @ManyToOne(() => Apps, (app: Apps) => app.id, {
  //   eager: false,
  //   nullable: false,
  // })
  // @JoinColumn()
  // app: Apps;

  @Column({
    type: 'enum',
    enum: EMPLOYEE_STATUS,
    default: EMPLOYEE_STATUS.IN_PROGRESS,
  })
  status: EMPLOYEE_STATUS;

  @Column({ type: 'json', nullable: true })
  bg_verification_document: any;

  @Column({ type: 'json', nullable: true })
  scheduled_interviews: any[];

  @Column({ type: 'boolean', default: false })
  is_rejected: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;
}

// @Entity('onboarding_employee_apps')
// export class OnboardingEmployeeApps {
//   @PrimaryColumn({ type: 'int' })
//   app_id: number;

//   @PrimaryColumn({ type: 'int' })
//   onboarding_employee_id: number;

//   @OneToOne(() => OnboardingEmployee)
//   @JoinTable()
//   onboarding_employee: OnboardingEmployee;

//   @OneToOne(() => Apps)
//   @JoinTable()
//   app: Apps;

//   @CreateDateColumn()
//   created_at: Date;

//   @UpdateDateColumn()
//   updated_at: Date;

//   @DeleteDateColumn()
//   deleted_at: Date;
// }
