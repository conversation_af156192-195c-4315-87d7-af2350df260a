import {
  Body,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
  Inject,
  forwardRef,
  Optional,
} from '@nestjs/common';
import { Model, QueryOptions } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { generateUUID } from 'src/util';
import { FormFields } from 'src/entities/mongodb/formfields.entity';
import {
  ClientsRepository,
  ClientsRepositoryDocument,
} from 'src/entities/mongodb/clientsrepository.entity';
import { CreateClientRepositoryDto } from 'src/dto/clientsrepository.dto';
import { AppsService } from './apps.service';
import { ConfigurationsService } from './configurations.service';

@Injectable()
export class ClientsRepositoryService {
  constructor(
    @InjectModel(ClientsRepository.name)
    private readonly ClientsRepository: Model<ClientsRepositoryDocument>,
    private readonly appsService: AppsService,
    @Optional()
    @Inject(forwardRef(() => ConfigurationsService))
    private readonly configurationsService: ConfigurationsService,
  ) {}

  /**
   * @variable select
   */

  private select: QueryOptions<ClientsRepository> = {
    _id: false,
    name: true,
    mobile_number: true,
    user_id: true,
    address: true,
    form_values: true,
    storage_folder_name: true,
    client_id: true,
    zoho_lead_id: true,
    createdAt: true,
    updatedAt: true,
    deleted_at: true,
  };

  /**
   * Create Client
   * @param data CreateClientRepositoryDto
   * @param user any
   * @returns Promise<ClientsRepository>
   */
  async create(
    data: CreateClientRepositoryDto,
    user: any,
    app_code: any,
  ): Promise<any> {
    try {
      if (!app_code) {
        throw new HttpException('App code is required', HttpStatus.BAD_REQUEST);
      }

      const appDetails = await this.appsService.findOne(
        { app_code },
        false,
        null,
        {
          industry_app_process: true,
        },
      );

      if (!appDetails) {
        throw new NotFoundException('App not found');
      }

      if (
        appDetails &&
        appDetails?.industry_app_process &&
        appDetails?.industry_app_process?.process_code == 'HC_CLIASS'
      ) {
        const clientData = {
          ...data,
          user_id: user['user_id'],
          app_id: user['app_id'],
          organization_id: user['organization'],
          client_id: generateUUID(),
        };

        // Create client in database
        const createdClient = await this.ClientsRepository.create(clientData);

        // Try to create lead in Zoho CRM if configured
        try {
          const organization_id = user['organization'];

          // Check if ConfigurationsService is available
          if (!this.configurationsService) {
            console.log(
              'ConfigurationsService not available, skipping Zoho CRM sync',
            );
            return createdClient;
          }

          // Check if webhook_client_to_lead is enabled in configuration details
          const organization = await this.configurationsService.getOrganization(
            organization_id,
          );
          const configurationDetails = await this.configurationsService.findOne(
            {
              type: 'webhook',
              organization: organization,
            },
          );

          if (
            !configurationDetails ||
            !configurationDetails.details?.webhook_client_to_lead ||
            configurationDetails.details?.webhook_client_to_lead !== true
          ) {
            console.log(
              'webhook_client_to_lead is not enabled, skipping Zoho CRM sync',
            );
            return createdClient;
          }

          const isZohoCrmConfigured =
            await this.configurationsService.isZohoCrmConfigured(
              organization_id,
            );

          if (isZohoCrmConfigured) {
            const leadData = {
              name: data.name,
              mobile_number: data.mobile_number,
              address: data.address,
              client_id: clientData.client_id,
            };

            const zohoCrmResult =
              await this.configurationsService.createZohoCrmLead(
                organization_id,
                leadData,
              );

            if (zohoCrmResult.success) {
              console.log(
                'Lead created successfully in Zoho CRM:',
                zohoCrmResult.zoho_lead_id,
              );
              // Store the Zoho lead ID in the client record
              await this.ClientsRepository.findOneAndUpdate(
                { client_id: clientData.client_id },
                { zoho_lead_id: zohoCrmResult.zoho_lead_id },
              );
            } else {
              console.warn(
                'Failed to create lead in Zoho CRM:',
                zohoCrmResult.message,
              );
            }
          }
        } catch (zohoCrmError) {
          // Log the error but don't fail the client creation
          console.error(
            'Error creating lead in Zoho CRM:',
            zohoCrmError.message,
          );
        }

        return createdClient;
      } else {
        throw new HttpException(
          'App code is not valid',
          HttpStatus.BAD_REQUEST,
        );
      }
    } catch (error) {
      throw error;
    }
  }

  async checkClientRequiredOrNot(app_code: any) {
    if (!app_code) {
      throw new HttpException('App code is required', HttpStatus.BAD_REQUEST);
    }

    const appDetails = await this.appsService.findOne(
      { app_code },
      false,
      null,
      {
        industry_app_process: true,
      },
    );

    if (!appDetails) {
      throw new NotFoundException('App not found');
    }

    if (
      appDetails &&
      appDetails?.industry_app_process &&
      appDetails?.industry_app_process?.process_code == 'HC_CLIASS'
    ) {
      return true;
    } else {
      return false;
    }
  }

  /**
   * Update the Client by client_id
   * @param data CreateClientRepositoryDto.
   * @param client_id string
   * @param user any
   * @returns Promise<ClientsRepository>
   */
  async update(
    client_id: string,
    data: CreateClientRepositoryDto,
  ): Promise<ClientsRepository> {
    try {
      const form = await this.findOne({ client_id });

      if (!form) {
        throw new NotFoundException();
      }

      return await this.ClientsRepository.findOneAndUpdate(
        {
          client_id: form.client_id,
        },
        data,
        { returnDocument: 'after' },
      ).select(this.select);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Find client record by condition
   * @param condition Object "{column: value}"
   * @returns Promise<ClientsRepository>
   */
  async findOne(
    condition: QueryOptions<ClientsRepository>,
    withDeleted = false,
    select: QueryOptions<ClientsRepository> = this.select,
  ): Promise<ClientsRepository> {
    try {
      if (withDeleted) {
        condition['deleted_at'] = { $ne: null };
      } else {
        condition['deleted_at'] = { $eq: null };
      }
      return await this.ClientsRepository.findOne(condition).select(select);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get All Form Clients
   * @returns Promise<ClientsRepository[]>
   */
  async findAll(
    condition: QueryOptions<ClientsRepository> = { status: true },
    withDeleted = false,
    select: QueryOptions<ClientsRepository> = this.select,
    order: any = { createdAt: -1 },
  ): Promise<ClientsRepository[]> {
    if (!condition) condition = { status: true };

    if (withDeleted) {
      condition['deleted_at'] = { $ne: null };
    } else {
      condition['deleted_at'] = { $eq: null };
    }
    if (!select) select = this.select;

    const list = await this.ClientsRepository.find(condition)
      .select(select)
      .sort(order);

    return list;
  }

  /**
   * Delete form client records by condition
   * @returns Promise<ClientsRepository>
   */
  async delete(client_id: string): Promise<ClientsRepository> {
    try {
      const client = await this.findOne({ client_id });

      if (!client) {
        throw new NotFoundException();
      }

      return await this.ClientsRepository.findOneAndUpdate(
        { client_id },
        {
          deleted_at: Date.now(),
        },
        {
          new: true,
        },
      );
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Sync existing client to Zoho CRM
   * @param client_id string
   * @param organization_id string
   * @returns Promise<any>
   */
  async syncClientToZohoCrm(
    client_id: string,
    organization_id: string,
  ): Promise<any> {
    try {
      // Check if ConfigurationsService is available
      if (!this.configurationsService) {
        throw new HttpException(
          'ConfigurationsService not available',
          HttpStatus.SERVICE_UNAVAILABLE,
        );
      }

      const client = await this.findOne({ client_id });

      if (!client) {
        throw new NotFoundException('Client not found');
      }

      // Check if already synced
      if (client.zoho_lead_id) {
        return {
          success: false,
          message: 'Client already synced to Zoho CRM',
          zoho_lead_id: client.zoho_lead_id,
        };
      }

      // Check if webhook_client_to_lead is enabled in configuration details
      const organization = await this.configurationsService.getOrganization(
        organization_id,
      );
      const configurationDetails = await this.configurationsService.findOne({
        type: 'webhook',
        organization: organization,
      });

      if (
        !configurationDetails ||
        !configurationDetails.details?.webhook_client_to_lead ||
        configurationDetails.details?.webhook_client_to_lead !== true
      ) {
        throw new HttpException(
          'webhook_client_to_lead is not enabled for this organization',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Check if Zoho CRM is configured
      const isZohoCrmConfigured =
        await this.configurationsService.isZohoCrmConfigured(organization_id);

      if (!isZohoCrmConfigured) {
        throw new HttpException(
          'Zoho CRM is not configured for this organization',
          HttpStatus.BAD_REQUEST,
        );
      }

      const leadData = {
        name: client.name,
        mobile_number: client.mobile_number,
        address: client.address,
        client_id: client.client_id,
      };

      const zohoCrmResult = await this.configurationsService.createZohoCrmLead(
        organization_id,
        leadData,
      );

      if (zohoCrmResult.success) {
        // Update client with Zoho lead ID
        await this.ClientsRepository.findOneAndUpdate(
          { client_id },
          { zoho_lead_id: zohoCrmResult.zoho_lead_id },
        );

        return {
          success: true,
          message: 'Client synced successfully to Zoho CRM',
          zoho_lead_id: zohoCrmResult.zoho_lead_id,
          data: zohoCrmResult.data,
        };
      } else {
        return {
          success: false,
          message: 'Failed to sync client to Zoho CRM',
          error: zohoCrmResult.error,
        };
      }
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong while syncing to Zoho CRM.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Bulk sync clients to Zoho CRM
   * @param organization_id string
   * @returns Promise<any>
   */
  async bulkSyncClientsToZohoCrm(organization_id: string): Promise<any> {
    try {
      // Check if ConfigurationsService is available
      if (!this.configurationsService) {
        throw new HttpException(
          'ConfigurationsService not available',
          HttpStatus.SERVICE_UNAVAILABLE,
        );
      }

      // Check if webhook_client_to_lead is enabled in configuration details
      const organization = await this.configurationsService.getOrganization(
        organization_id,
      );
      const configurationDetails = await this.configurationsService.findOne({
        type: 'webhook',
        organization: organization,
      });

      if (
        !configurationDetails ||
        !configurationDetails.details?.webhook_client_to_lead ||
        configurationDetails.details?.webhook_client_to_lead !== true
      ) {
        throw new HttpException(
          'webhook_client_to_lead is not enabled for this organization',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Check if Zoho CRM is configured
      const isZohoCrmConfigured =
        await this.configurationsService.isZohoCrmConfigured(organization_id);

      if (!isZohoCrmConfigured) {
        throw new HttpException(
          'Zoho CRM is not configured for this organization',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Get all clients without Zoho lead ID
      const clientsToSync = await this.findAll({
        organization_id,
        zoho_lead_id: { $exists: false },
      });

      const results = {
        total: clientsToSync.length,
        synced: 0,
        failed: 0,
        errors: [],
      };

      for (const client of clientsToSync) {
        try {
          const syncResult = await this.syncClientToZohoCrm(
            client.client_id,
            organization_id,
          );
          if (syncResult.success) {
            results.synced++;
          } else {
            results.failed++;
            results.errors.push({
              client_id: client.client_id,
              error: syncResult.message,
            });
          }
        } catch (error) {
          results.failed++;
          results.errors.push({
            client_id: client.client_id,
            error: error.message,
          });
        }
      }

      return results;
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong during bulk sync.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
