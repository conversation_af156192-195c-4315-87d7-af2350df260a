import { MongooseModule } from '@nestjs/mongoose';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Admin } from 'src/entities/admin.entity';
import { Apps } from 'src/entities/apps.entity';
import { Themes } from 'src/entities/themes.entity';
import {
  Organization,
  OrganizationApps,
} from 'src/entities/organization.entity';
import { User, UserApps } from 'src/entities/user.entity';
import { Configurations } from 'src/entities/configurations.entity';
import { IndustryTypes } from 'src/entities/industry-types.entity';
import { IndustryAppProcess } from 'src/entities/industry-app-process.entity';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import { OnboardingEmployee } from 'src/entities/onboarding-employee.entity';
import { Caregivers } from 'src/entities/caregivers.entity';
import { Stages } from 'src/entities/stage.entity';

// MySQL Connection using TypeORM
export const MySQLConnection = TypeOrmModule.forRootAsync({
  name: 'mysql',
  useFactory: () => ({
    type: 'mysql',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 3306,
    username: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    // autoLoadEntities: true,
    entities: [
      Apps,
      User,
      Admin,
      Themes,
      UserApps,
      Organization,
      OrganizationApps,
      Configurations,
      IndustryTypes,
      IndustryAppProcess,
      OrgAppConfiguration,
      OnboardingEmployee,
      Caregivers,
      Stages,
    ],
    synchronize: process.env.DB_SYNC === 'true' ? true : false,
    logging: true,
    logger: 'file',
  }),
});

//MongoDb Connection using Mongoose
export const MongoDBConnection = MongooseModule.forRootAsync({
  useFactory: () => ({
    // uri: `mongodb://${process.env.MONGODB_HOST}:${process.env.MONGODB_PORT}/${process.env.DB_NAME}`,
    uri: process.env.MONGODB_URI,
  }),
});
